import { i18nRouter } from 'next-i18n-router';
import type { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';

import i18nConfig from '@/config/i18n';

// List of routes to redirect to 404
const routesToBlock = ['/final-placements', '/brackets', '/leaderboard', '/event-schedule', '/side-events'];

export function middleware(request: NextRequest) {
  const path = request.nextUrl.pathname;

  if (routesToBlock.some((route) => path.includes(route))) {
    return NextResponse.redirect(new URL('/404', request.url));
  }

  const response = i18nRouter(request, i18nConfig);

  response.headers.set('x-pathname', path);

  // Set cache headers for static pages
  if (path === '/' || path.includes('/rules')) {
    response.headers.set('Cache-Control', 'public, max-age=300, s-maxage=300, stale-while-revalidate=86400');
  }

  return response;
}

// only applies this middleware to files in the app directory
export const config = {
  matcher: '/((?!api|static|.*\\..*|_next).*)',
};
