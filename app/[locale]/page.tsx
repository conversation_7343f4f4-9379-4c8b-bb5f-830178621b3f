import { getPageConfig, getSiteConfig } from '@/apis/strapi';
import {
  Carousel,
  FAQ,
  HomepageHero,
  InfoBlock,
  SpectatorPassBlock,
  TicketsInfoBlock,
  WaysToPlay,
} from '@/components/contentstack';
import {
  getAltInfoBlockProps,
  getBrillianceBundleInfoProps,
  getCarouselProps,
  getFAQProps,
  getHeroProps,
  getInfoBlockProps,
  getTicketsInfoProps,
  getWaysToPlayProps,
} from '@/data/homepage';
import { PageProps } from '@/types/router';
import { HomeConfig } from '@/types/strapi';
import { Box, FlexLayout } from '@/ui';

export * from '@/config/static-page-cache';

export default async function Home({ params: { locale } }: PageProps) {
  const [siteConfig, pageConfig] = await Promise.all([
    getSiteConfig(locale),
    getPageConfig<HomeConfig>('page-home', locale),
  ]);

  const props = { siteConfig, pageConfig, locale };

  return (
    <Box
      sx={{
        backgroundImage: 'url(/images/homeBackground.jpg)',
        backgroundRepeat: 'no-repeat',
        backgroundSize: 'cover',
        // backgroundPosition: [
        //   '50% -50px, 50% 1776px, 50% 5014px',
        //   '50% -100px, 50% 1776px, 50% 5014px',
        //   '50% -200px, 50% 1776px, 50% 5014px',
        // ],
      }}
    >
      <Box sx={{ position: 'relative', overflow: 'hidden' }}>
        <HomepageHero {...getHeroProps(props)} />
        <InfoBlock {...getInfoBlockProps(props)} />
        {/* <Passport title={siteConfig.localeJSON['passport']} /> */}
        <WaysToPlay {...getWaysToPlayProps(props)} />
        <FlexLayout space={[14, 20, 40]} mt={[0, 0, 25]} flexDirection="column">
          <TicketsInfoBlock {...getTicketsInfoProps(props)} reverse />
          <TicketsInfoBlock {...getBrillianceBundleInfoProps(props)} />
          <SpectatorPassBlock {...getAltInfoBlockProps(props)} />
        </FlexLayout>
        <Carousel {...getCarouselProps(props)} />
        <FlexLayout my={[12, 24, 24]} mx={[4, 6]}>
          <FAQ {...getFAQProps(props)} maxWidth="1022px" withBorder={false} />
        </FlexLayout>
      </Box>
    </Box>
  );
}
