import { Box, FlexLayout, Text, Tooltip } from '@/ui';
import { variants } from '@/ui/theme/typography';

interface Props {
  text: string;
  explainerTitle: string;
  explainerContentHtml: string;
}

export const RulesExplainer = ({ text, explainerTitle, explainerContentHtml }: Props) => {
  console.log('rules explainer');
  return (
    <Tooltip
      side="bottom"
      arrowColor="white"
      content={
        <Box px={5} py={4} backgroundColor="white" sx={{ maxWidth: 476 }}>
          <FlexLayout flexDirection={'column'} space={3} alignItems={'center'}>
            <Text variant="paragraph-m-medium" color="midnight900">
              {explainerTitle}
            </Text>
            <Box
              color="midnight900"
              sx={{
                p: { m: 0, ...variants['paragraph-s-regular'] },
                ul: { mb: 0, pl: '24px', ...variants['paragraph-s-regular'] },
              }}
              dangerouslySetInnerHTML={{ __html: explainerContentHtml }}
            ></Box>
          </FlexLayout>
        </Box>
      }
    >
      <Text as="p" variant="paragraph-s-medium" color="paleYellow600" sx={{ cursor: 'pointer', width: 'fit-content' }}>
        {text}
      </Text>
    </Tooltip>
  );
};
