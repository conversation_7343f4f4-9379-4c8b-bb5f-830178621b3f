import { getPageConfig, getSiteConfig } from '@/apis/strapi';
import { Container } from '@/components';
import { getRulesProps } from '@/data/rules';
import { PageProps } from '@/types/router';
import { RulesPage } from '@/types/strapi';
import { Box, FlexLayout, Text } from '@/ui';

export * from '@/config/page-cache';

// TODO: Add/replace the actual PDF URL (pdfBaseUrl) when available
const pdfBaseUrl = 'https://tft-web-mail-assets.s3.us-east-2.amazonaws.com/tft-paris-rulebook.pdf';

// The PDF url with parameters to control the viewer's appearance
const pdfUrl = `${pdfBaseUrl}#toolbar=0&navpanes=0&scrollbar=0&view=FitH&zoom=100`;

export default async function Rules({ params: { locale } }: PageProps) {
  const [siteConfig, pageConfig] = await Promise.all([
    getSiteConfig(locale),
    getPageConfig<RulesPage>('page-rules', locale),
  ]);

  const rules = getRulesProps({ siteConfig, pageConfig });

  return (
    <Box
      sx={{
        backgroundColor: 'secondary-lemonade',
        backgroundImage: 'url(/images/rulesBackground-light.png)',
        backgroundRepeat: 'no-repeat',
        backgroundSize: ['cover', 'cover', '100% auto'],
        backgroundPosition: 'center top',
        backgroundAttachment: 'fixed',
        minHeight: '100vh',
      }}
    >
      <Container>
        <FlexLayout alignItems="center" flexDirection="column" my={[16, 20, 30]} mx={[4, 6]} space={[10, 12, 20]}>
          <Text textVariant={['h4', 'h3']} upperCase isCentered mx={[6, 6, 15]} color="primary-midnight">
            {rules.title}
          </Text>
          <FlexLayout
            alignItems="center"
            bg="white"
            flexDirection="column"
            mx={[0, 6, 15]}
            py={0}
            px={0}
            sx={{ maxWidth: '100%', minWidth: '100%', height: ['600px', '600px', '800px'] }}
          >
            {/* PDF Embed */}
            {pdfBaseUrl && (
              <Box sx={{ width: '100%', height: '100%' }}>
                <iframe
                  src={pdfUrl}
                  style={{
                    width: '100%',
                    height: '100%',
                    border: 'none',
                    display: 'block',
                  }}
                  title="Rules PDF"
                />
              </Box>
            )}
          </FlexLayout>
        </FlexLayout>
      </Container>
    </Box>
  );
}
